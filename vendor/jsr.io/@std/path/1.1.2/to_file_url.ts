// Copyright 2018-2025 the Deno authors. MIT license.
// This module is browser compatible.

import { isWindows } from "jsr:@std/internal@^1.0.10/os";
import { toFileUrl as posixToFileUrl } from "./posix/to_file_url.ts";
import { toFileUrl as windowsToFileUrl } from "./windows/to_file_url.ts";

/**
 * Converts a path string to a file URL.
 *
 * @example Usage
 * ```ts
 * import { toFileUrl } from "@std/path/to-file-url";
 * import { assertEquals } from "@std/assert";
 *
 * if (Deno.build.os === "windows") {
 *   assertEquals(toFileUrl("\\home\\foo"), new URL("file:///home/<USER>"));
 *   assertEquals(toFileUrl("C:\\Users\\<USER>\\\\127.0.0.1\\home\\foo"), new URL("file://127.0.0.1/home/<USER>"));
 * } else {
 *   assertEquals(toFileUrl("/home/<USER>"), new URL("file:///home/<USER>"));
 * }
 * ```
 *
 * @param path Path to convert to file URL.
 * @returns The file URL equivalent to the path.
 */
export function toFileUrl(path: string): URL {
  return isWindows ? windowsToFileUrl(path) : posixToFileUrl(path);
}
