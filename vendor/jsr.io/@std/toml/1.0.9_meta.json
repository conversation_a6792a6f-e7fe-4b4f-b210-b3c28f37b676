{"manifest": {"/LICENSE": {"size": 1075, "checksum": "sha256-0961f97da6619d5fe9ddb98649191d5ca6e958856ea5252f4cce7c9b85513819"}, "/_parser_test.ts": {"size": 15516, "checksum": "sha256-8940b8f657c63d5f5bae641c3e28a9357e5fd149e77ef9f270b4b4f643ac1777"}, "/mod.ts": {"size": 2913, "checksum": "sha256-b41c60d79728e8b711489ba89252e1a9917cefbbdc2cdf76d5cdfaffa62d5c33"}, "/_parser.ts": {"size": 23401, "checksum": "sha256-c544e7d288754225b87fd1da250fad07af984e766acce01d03158d7fe003d0a9"}, "/stringify_test.ts": {"size": 5382, "checksum": "sha256-4bcfea76a9bbc6adf8c2739d2c0860f45731bea6f4c261962157910de8aa3e3c"}, "/parse.ts": {"size": 799, "checksum": "sha256-f6076c2b7a25cf89ffc811605edb44c2f907a50c4a0c19d66e527905a64ed815"}, "/stringify.ts": {"size": 9370, "checksum": "sha256-b1b70fb3202a773ececf2b2acc0e272929fc94bca6ed98d79989938520c27429"}, "/deno.json": {"size": 154, "checksum": "sha256-fd121379768a6ed67909be4d80b4fbd893d53e7ecf778800f0baff72768b77da"}, "/parse_test.ts": {"size": 21106, "checksum": "sha256-83c528666d5f3f25efba6b98cc9eaa615c39df553cb0851caca1275f6d85d097"}}, "exports": {".": "./mod.ts", "./parse": "./parse.ts", "./stringify": "./stringify.ts"}, "lockfileChecksum": "1629147c2edbffd71be6ab66cf90d3b27e59ca314067e58649e0626979383b83"}