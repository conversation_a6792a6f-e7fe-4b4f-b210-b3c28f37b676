import { parseArgs } from "jsr:@std/cli/parse-args";
import { getEnvironment } from "@ozaco/monorepo";

const args = parseArgs(Deno.args, {
  boolean: ["help", "version"],
  alias: {
    h: "help",
    v: "version",
  },
});

if (args._.includes("build")) {
  const buildTarget = `${args._.slice(args._.indexOf("build") + 1).at(0)}`;

  if (!buildTarget) {
    throw new Error("No build target provided");
  }

  const environment = await getEnvironment(buildTarget);

  console.log(environment, buildTarget);
} else {
  throw new Error("Unknown command");
}
