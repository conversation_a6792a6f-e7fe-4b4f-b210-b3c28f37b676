import { exists } from "jsr:@std/fs";
import { join } from "jsr:@std/path";

import { importConfig } from "./config.ts";
import type { Environment, EnvironmentConfig } from "./types.ts";

export const getEnvironment = async (path: string, providedCwd?: string) => {
  const cwd = join(providedCwd ?? Deno.cwd(), path);
  const homedir = Deno.env.get("HOME") ?? cwd;

  const cwdLevel = cwd.split("/");
  const homedirLevel = homedir.split("/");

  const configFilePaths: string[] = [];
  let root: string | null = null;

  for (let i = cwdLevel.length; i >= homedirLevel.length; i--) {
    const level = cwdLevel.slice(0, i).join("/");

    const configFile = join(level, "ozaco.toml");

    if (await exists(configFile)) {
      configFilePaths.unshift(configFile);

      root = level;
    }
  }

  if (configFilePaths.length === 0 || !root) {
    throw new Error("No ozaco.toml found");
  }

  const configs: Environment["configs"] = [];

  for (const configFilePath of configFilePaths) {
    const config = await importConfig(configFilePath, root);

    configs.push(config);
  }

  const config = configs.reduce((acc, curr) => {
    if (curr.root) {
      (acc as EnvironmentConfig).workspace = curr;
    } else {
      // biome-ignore lint/performance/noAccumulatingSpread: Redundant
      Object.assign(acc, curr);
    }

    return acc;
  }, {});

  return {
    root,

    config,
    configs,
  };
};
