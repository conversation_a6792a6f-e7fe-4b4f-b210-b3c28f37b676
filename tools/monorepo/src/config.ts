import { join } from "@std/path";
import { parse } from "@std/toml";

import type { EnvironmentConfig, EnvironmentWorkspaceConfig } from "./types.ts";

export const importConfig = async (path: string, cwd: string = Deno.cwd()) => {
  const configData = await Deno.readTextFile(join(cwd, path));

  const config = (parse(configData) ?? {}) as unknown as EnvironmentConfig | EnvironmentWorkspaceConfig;

  return config;
};
