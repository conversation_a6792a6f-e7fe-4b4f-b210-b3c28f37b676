{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "bracketSameLine": true, "attributePosition": "multiline", "lineWidth": 180, "bracketSpacing": true, "lineEnding": "lf"}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}